{"className": "com.thedasagroup.suminative.ui.stock.StockScreenDialogsKt$ChangeStockDialog$stockResponse$2", "classAnnotations": [], "interfaces": ["java.io.Serializable", "kotlin.Function", "kotlin.jvm.functions.Function1", "kotlin.reflect.KAnnotatedElement", "kotlin.reflect.<PERSON>", "kotlin.reflect.<PERSON>ty", "kotlin.reflect.KProperty1"], "superClasses": ["kotlin.jvm.internal.PropertyReference1Impl", "kotlin.jvm.internal.PropertyReference1", "kotlin.jvm.internal.PropertyReference", "kotlin.jvm.internal.CallableReference", "java.lang.Object"]}