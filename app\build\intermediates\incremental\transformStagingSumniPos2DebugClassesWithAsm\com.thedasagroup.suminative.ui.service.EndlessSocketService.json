{"className": "com.thedasagroup.suminative.ui.service.EndlessSocketService", "classAnnotations": ["dagger.hilt.android.AndroidEntryPoint", "androidx.compose.runtime.internal.StabilityInferred", "kotlin.jvm.internal.SourceDebugExtension"], "interfaces": ["android.content.ComponentCallbacks", "android.content.ComponentCallbacks2"], "superClasses": ["android.app.Service", "android.content.ContextWrapper", "android.content.Context", "java.lang.Object"]}