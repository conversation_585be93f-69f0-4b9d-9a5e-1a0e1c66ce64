{"className": "com.thedasagroup.suminative.ui.reservations.ReservationsFragment", "classAnnotations": ["androidx.compose.runtime.internal.StabilityInferred", "kotlin.jvm.internal.SourceDebugExtension"], "interfaces": ["android.content.ComponentCallbacks", "android.view.View$OnCreateContextMenuListener", "androidx.activity.result.ActivityResultCaller", "androidx.lifecycle.HasDefaultViewModelProviderFactory", "androidx.lifecycle.LifecycleOwner", "androidx.lifecycle.ViewModelStoreOwner", "androidx.savedstate.SavedStateRegistryOwner", "com.airbnb.mvrx.MavericksView", "com.airbnb.mvrx.mocking.MockableMavericksView"], "superClasses": ["androidx.fragment.app.Fragment", "java.lang.Object"]}