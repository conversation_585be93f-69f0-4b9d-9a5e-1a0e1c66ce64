{"className": "com.thedasagroup.suminative.ui.reservations.ReservationsMocksKt$mocks$2$1$2$1", "classAnnotations": [], "interfaces": ["java.io.Serializable", "kotlin.Function", "kotlin.jvm.functions.Function0", "kotlin.reflect.KAnnotatedElement", "kotlin.reflect.<PERSON>", "kotlin.reflect.<PERSON>ty", "kotlin.reflect.KProperty0"], "superClasses": ["kotlin.jvm.internal.PropertyReference0Impl", "kotlin.jvm.internal.PropertyReference0", "kotlin.jvm.internal.PropertyReference", "kotlin.jvm.internal.CallableReference", "java.lang.Object"]}