{"className": "com.thedasagroup.suminative.ui.stock.ChangeStockUseCase$invoke$1", "classAnnotations": ["kotlin.coroutines.jvm.internal.DebugMetadata"], "interfaces": ["java.io.Serializable", "kotlin.coroutines.Continuation", "kotlin.coroutines.jvm.internal.CoroutineStackFrame"], "superClasses": ["kotlin.coroutines.jvm.internal.ContinuationImpl", "kotlin.coroutines.jvm.internal.BaseContinuationImpl", "java.lang.Object"]}